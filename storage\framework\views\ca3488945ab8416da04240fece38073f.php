<div class="col-md-12">
    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
        <li class="nav-item" role="presentation">
            <a href="<?php echo e(route('services.create', ["type" => "individual"])); ?>" class="nav-link <?php echo e($type == 'individual' ? 'active' : ''); ?>  business-services"><?php echo $__env->make('svg.individula', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                Individual Services
            </a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link business-services <?php echo e($type == 'group' ? 'active' : ''); ?>" href="<?php echo e(route('services.create', ["type" => "group"])); ?>">
                <?php echo $__env->make('svg.group', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                Group Service
            </a>
        </li>
    </ul>
    <div >
        <?php echo $__env->renderWhen($type == 'individual', 'dashboard.service.include.individual', ["btn_text" => $btn_text ?? "Add"], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path'])); ?>
        <?php echo $__env->renderWhen($type == 'group', 'dashboard.service.include.group', ["btn_text" => $btn_text ?? "Add"], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path'])); ?>
    </div>
    
</div>

<?php $__env->startPush("js"); ?>
    <script>
        $(document).ready(function () {
            // Initialize Select2 on page load
            $('#subcategory').select2();

            // Check if there's an old category value (for validation errors)
            var oldCategoryId = '<?php echo e(old("category_id")); ?>';
            var oldSubcategoryId = '<?php echo e(old("subcategory_id")); ?>';

            if(oldCategoryId && oldCategoryId !== '') {
                // Load subcategories for the old category value
                loadSubcategories(oldCategoryId, oldSubcategoryId);
            }

            $(document).on('change', '#category', function () {
                let category_id = $(this).val();
                loadSubcategories(category_id);
            });

            function loadSubcategories(category_id, selectedSubcategoryId = '') {
                let subcategory = $('#subcategory');

                if(category_id != ''){
                    $.ajax({
                        url: `<?php echo e(route('subcategories.get', "")); ?>/${category_id}`,
                        type: 'GET',
                        data: {
                            category_id: category_id
                        },
                        success: function(response) {
                            if(response.status){
                                // Clear existing options
                                subcategory.empty();
                                subcategory.append('<option value="">Select Subcategory</option>');

                                // Add new options
                                response.data.forEach(element => {
                                    var isSelected = selectedSubcategoryId && selectedSubcategoryId == element.ids ? 'selected' : '';
                                    subcategory.append(`<option value="${element.ids}" ${isSelected}>${element.name}</option>`);
                                });

                                // Trigger change to update Select2
                                subcategory.trigger('change');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Error fetching subcategories:', error);
                            alert('An error occurred while fetching subcategories');
                        }
                    });
                } else {
                    // Clear subcategory when no category is selected
                    subcategory.empty();
                    subcategory.append('<option value="">Select Subcategory</option>');
                    subcategory.trigger('change');
                }
            }
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\git-file\anders\resources\views/dashboard/service/form.blade.php ENDPATH**/ ?>