<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid booking">
        <div id="kt_app_content_container" class="app-container container padding-block booking-section">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="semi_bold sora black">Bookings</h6>
                        <p class="fs-14 normal sora light-black">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    <?php if(auth()->check() && auth()->user()->hasAnyRole(['individual', 'business'])): ?>
                        <a href="#!" class="add-btn" data-bs-target="#add-booking" data-bs-toggle="modal"> <i
                                class="fa-solid fa-plus me-3"></i> Add Booking </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="row row-gap-5 mb-10 card-wrapper">
                <div class="col-xl-4 col-lg-6 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-blue">
                                    <?php echo $__env->make('svg.customer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Total Bookings
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="2420">
                                </p>
                            </div>
                            <div class="card-footer ">
                                <div class="fs-12 w-700 green-box green">
                                    <i class="fa-solid fa-arrow-up analytics-green-arrow"></i>
                                    17.2%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-xl-4 col-lg-6 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-purple">
                                    <?php echo $__env->make('svg.professional', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px ">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Active Bookings
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="1869">
                                </p>
                            </div>
                            <div class="card-footer">
                                <div class="fs-12 w-700 green-box green">
                                    <i class="fa-solid fa-arrow-up analytics-green-arrow"></i>
                                    17.2%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-xl-4 col-lg-6 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-green">
                                    <?php echo $__env->make('svg.user', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px ">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Upcoming Bookings
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="241">
                                </p>
                            </div>
                            <div class="card-footer">
                                <div class="fs-12 w-700 red-box red">
                                    <i class="fa-solid fa-arrow-down analytics-red-arrow"></i>
                                    -2.3%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business')): ?>
                        <div class="table-container">
                            <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                <ul class="ms-auto booking-tabs nav nav-pills mb-3 justify-content-end" id="view-tab"
                                    role="tablist">
                                    <li class="nav-item " role="presentation">
                                        <button class="nav-link active calendar-view " id="list-tab" data-bs-toggle="pill"
                                            data-bs-target="#list-view" type="button" role="tab" aria-controls="list-view"
                                            aria-selected="true">
                                            <i class="fa-solid fa-list me-2"></i> List View
                                        </button>
                                    </li>
                                    <li class="nav-item " role="presentation">
                                        <button class="nav-link calendar-view" id="calendar-tab" data-bs-toggle="pill"
                                            data-bs-target="#calendar-view" type="button" role="tab"
                                            aria-controls="calendar-view" aria-selected="false">
                                            <i class="fa-regular fa-calendar me-2"></i> Calendar View
                                        </button>
                                    </li>
                                </ul>
                            </div>
                            <div class="tab-content" id="view-tabContent">
                                <div class="tab-pane fade show active" id="list-view" role="tabpanel" aria-labelledby="list-tab"
                                    tabindex="0">
                                    <div class="d-flex gap-3 align-items-center flex-wrap">
                                        <!-- 📝 List View Content -->
                                        <div class="search_box">
                                            <label for="customSearchInput">
                                                <i class="fas fa-search"></i>
                                            </label>
                                            <input class="search_input search" type="text" id="customSearchInput"
                                                placeholder="Search..." />
                                        </div>
                                        <!-- Select with dots -->
                                        <div class="dropdown search_box select-box">
                                            <button
                                                class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                                type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <span><span class="dot"></span>
                                                    All</span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                                        data-color="#4B5563"><span class="dot all"></span>
                                                        All</a></li>
                                                <li><a class="dropdown-item dropdown-status" href="#" data-label="Ongoing"
                                                        data-color="#F59E0B"><span class="dot ongoing"></span>
                                                        Ongoing</a></li>
                                                <li><a class="dropdown-item dropdown-status" href="#" data-label="Upcoming"
                                                        data-color="#3B82F6"><span class="dot upcoming"></span>
                                                        Upcoming</a></li>
                                                <li><a class="dropdown-item dropdown-status" href="#" data-label="Complete"
                                                        data-color="#10B981"><span class="dot completed"></span>
                                                        Complete</a></li>
                                                <li><a class="dropdown-item dropdown-status" href="#" data-label="Canceled"
                                                        data-color="#EF4444"><span class="dot cancelled-dot"></span>
                                                        Canceled</a></li>
                                            </ul>
                                        </div>

                                        <!-- category -->
                                        <div class="search_box select-box">
                                            <select class="search_input">
                                                <option value="Category">Category</option>
                                                <option value="all">All</option>
                                                <option value=" Group"> Group</option>
                                                <option value="Individual">Individual</option>
                                            </select>
                                        </div>
                                        <!-- category -->
                                        <?php if(auth()->check() && auth()->user()->hasRole('business')): ?>
                                            <div class="search_box select-box">
                                                <select class="search_input">
                                                    <option value="Staff">Staff</option>
                                                    <option value="all">All</option>
                                                    <option value=" Group"> Group</option>
                                                    <option value="Individual">Individual</option>
                                                </select>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Date Picker -->
                                        <!-- Date Picker -->
                                            <label for="datePicker" class="date_picker">
                                                <div class="date-picker-container">
                                                    <i class="bi bi-calendar-event calender-icon"></i>
                                                    <input type="text" name="datePicker" class="datePicker w-200px ms-3">
                                                    <i class="fa fa-chevron-down down-arrow ms-9"></i>

                                                </div>
                                            </label>
                                    </div>

                                    <table id="responsiveTable" class="responsiveTable display nowrap w-100">
                                        <thead>
                                            <tr>
                                                <?php if(auth()->check() && auth()->user()->hasAnyRole(['individual'])): ?>
                                                    <th>Booking ID</th>
                                                <?php endif; ?>
                                                <th>Customer Name</th>
                                                <th>Service Name</th>
                                                <th>Service Name</th>
                                                <th>Status</th>
                                                <th>Date & Time</th>
                                                <th>Amount</th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php for($b = 0; $b < 20; $b++): ?>
                                                <tr>
                                                    <?php if(auth()->check() && auth()->user()->hasAnyRole(['individual'])): ?>
                                                        <td data-label="Booking ID">BK-9123</td>
                                                    <?php endif; ?>
                                                    <td data-label="Customer Name">Carolyn Perkins</td>
                                                    <td data-label="Service Name">Hair Coloring</td>
                                                    <td data-label="Service Name">Individual</td>
                                                    <td data-label="Status" class="status paid-status">Booked</td>
                                                    <td data-label="Date & Time">Apr 10, 2025 - 2:00 PM</td>
                                                    <td data-label="Amount">$17.84</td>
                                                    <td data-label="Action">
                                                        <div class="dropdown">
                                                            <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="bi bi-three-dots-vertical"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                                <li>
                                                                    <button class="dropdown-item complete fs-14 regular "
                                                                        type="button">
                                                                        <i class="bi bi-check-circle complete-icon"></i>
                                                                        Mark as Complete
                                                                    </button>
                                                                </li>
                                                                <li>
                                                                    <button class="dropdown-item cancel fs-14 regular"
                                                                        type="button">
                                                                        <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                                    </button>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <?php if(auth()->check() && auth()->user()->hasAnyRole(['individual'])): ?>
                                                        <td data-label="Booking ID">BK-9123</td>
                                                    <?php endif; ?>
                                                    <td data-label="Customer Name">Carolyn Perkins</td>
                                                    <td data-label="Service Name">Hair Coloring</td>
                                                    <td data-label="Service Name">Individual</td>
                                                    <td data-label="Status" class="status unpaid-status">Cancelled</td>
                                                    <td data-label="Date & Time">Apr 10, 2025 - 2:00 PM</td>
                                                    <td data-label="Amount">$17.84</td>
                                                    <td data-label="Action">
                                                        <div class="dropdown">
                                                            <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="bi bi-three-dots-vertical"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                                <li>
                                                                    <button class="dropdown-item complete fs-14 regular "
                                                                        type="button">
                                                                        <i class="bi bi-check-circle complete-icon"></i>
                                                                        Mark as Complete
                                                                    </button>
                                                                </li>
                                                                <li>
                                                                    <button class="dropdown-item cancel fs-14 regular"
                                                                        type="button">
                                                                        <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                                    </button>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <?php if(auth()->check() && auth()->user()->hasAnyRole(['individual'])): ?>
                                                        <td data-label="Booking ID">BK-9123</td>
                                                    <?php endif; ?>
                                                    <td data-label="Customer Name">Carolyn Perkins</td>
                                                    <td data-label="Service Name">Hair Coloring</td>
                                                    <td data-label="Service Name">Individual</td>
                                                    <td data-label="Status" class="status pending-status">Pending</td>
                                                    <td data-label="Date & Time">Apr 10, 2025 - 2:00 PM</td>
                                                    <td data-label="Amount">$17.84</td>
                                                    <td data-label="Action">
                                                        <div class="dropdown">
                                                            <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="bi bi-three-dots-vertical"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                                <li>
                                                                    <button class="dropdown-item complete fs-14 regular "
                                                                        type="button">
                                                                        <i class="bi bi-check-circle complete-icon"></i>
                                                                        Mark as Complete
                                                                    </button>
                                                                </li>
                                                                <li>
                                                                    <button class="dropdown-item cancel fs-14 regular"
                                                                        type="button">
                                                                        <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                                    </button>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <?php if(auth()->check() && auth()->user()->hasAnyRole(['individual'])): ?>
                                                        <td data-label="Booking ID">BK-9123</td>
                                                    <?php endif; ?>
                                                    <td data-label="Customer Name">Carolyn Perkins</td>
                                                    <td data-label="Service Name">Hair Coloring</td>
                                                    <td data-label="Service Name">Individual</td>
                                                    <td data-label="Status" class="status reschedule-status">Reschedule</td>
                                                    <td data-label="Date & Time">Apr 10, 2025 - 2:00 PM</td>
                                                    <td data-label="Amount">$17.84</td>
                                                    <td data-label="Action">
                                                        <div class="dropdown">
                                                            <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="bi bi-three-dots-vertical"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                                <li>
                                                                    <button class="dropdown-item complete fs-14 regular "
                                                                        type="button">
                                                                        <i class="bi bi-check-circle complete-icon"></i>
                                                                        Mark as Complete
                                                                    </button>
                                                                </li>
                                                                <li>
                                                                    <button class="dropdown-item cancel fs-14 regular"
                                                                        type="button">
                                                                        <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                                    </button>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endfor; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="tab-pane fade" id="calendar-view" role="tabpanel" aria-labelledby="calendar-tab"
                                    tabindex="0">
                                    <!-- 📅 Calendar View Content -->
                                    <div class="row">

                                        <div class="col-lg-12  p-0">
                                            <div class="schedule-container ">
                                                <div class="calendar-header flex-align-space-btw">
                                                    <div class="flex-align-space-btw mb-10">
                                                        <div class="calendar-controls d-flex  gap-2 align-items-center">
                                                            <div class="d-flex gap-4">
                                                                <p id="today" class="m-0 fs-13 regular black">Today</p>
                                                                <button id="prev-week" class="btn-prev"><i
                                                                        class="fa-solid fa-chevron-left"></i></button>
                                                                <button id="next-week" class="btn-next"><i
                                                                        class="fa-solid fa-chevron-right"></i></button>
                                                            </div>
                                                            <h3 id="date-range" class="m-0 fs-16 semi-bold black">14 July 2025 -
                                                                20 July 2025
                                                            </h3>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php if(auth()->user()->hasRole('business')): ?>
                                                    <div class="d-flex gap-4 flex-wrap mb-10">
                                                        <div class="user-option">
                                                            <input type="checkbox" id="user1" name="user" checked>
                                                            <label for="user1">
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/user-image1.png"
                                                                    class="h-30px w-30px object-fit-contain rounded-pill"
                                                                    alt="user-image">
                                                                <span>Charlie Dias</span>
                                                                <span class="checkmark">✔</span>
                                                            </label>
                                                        </div>

                                                        <div class="user-option">
                                                            <input type="checkbox" id="user2" name="user">
                                                            <label for="user2">
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/user-image2.png"
                                                                    class="h-30px w-30px object-fit-contain rounded-pill"
                                                                    alt="user-image">
                                                                <span>Marcus Septimus</span>
                                                                <span class="checkmark">✔</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                                <div id="calendar"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                        <div class="table-container">
                            <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                <div class="search_box">
                                    <label for="customSearchInput">
                                        <i class="fas fa-search"></i>
                                    </label>
                                    <input class="search_input search" type="text" id="customSearchInput"
                                        placeholder="Search..." />
                                </div>
                                <!-- Select with dots -->
                                <div class="dropdown search_box select-box">
                                    <button
                                        class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                        type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <span><span class="dot"></span>
                                            All</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                                data-color="#4B5563"><span class="dot all"></span>
                                                All</a></li>
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="Ongoing"
                                                data-color="#F59E0B"><span class="dot ongoing"></span>
                                                Ongoing</a></li>
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="Upcoming"
                                                data-color="#3B82F6"><span class="dot ongoing"></span>
                                                Upcoming</a></li>
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="Complete"
                                                data-color="#10B981"><span class="dot completed"></span>
                                                Complete</a></li>
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="Canceled"
                                                data-color="#EF4444"><span class="dot cancelled-dot"></span>
                                                Canceled</a></li>
                                    </ul>
                                </div>
                                <!-- category -->
                                <div class="search_box select-box">
                                    <select class="search_input">
                                        <option value="Category">Category</option>
                                        <option value="All">All</option>
                                        <option value="Group"> Group</option>
                                        <option value="Individual">Individual</option>
                                    </select>
                                </div>

                                <!-- Date Picker -->
                                            <label for="datePicker" class="date_picker">
                                                <div class="date-picker-container">
                                                    <i class="bi bi-calendar-event calender-icon"></i>
                                                    <input type="text" name="datePicker" class="datePicker w-200px ms-3">
                                                    <i class="fa fa-chevron-down down-arrow ms-9"></i>

                                                </div>
                                            </label>
                                            <div class="search_box d-block ms-auto">
                                                <a href="#!" class="search_input fs-14 normal link-gray ">
                                                    Export <i class="bi bi-file-arrow-down ms-1 file-icon"></i>
                                                </a>
                                            </div>

                            </div>
                            <!-- 📝 List View Content -->
                            <table id="responsiveTable" class=" responsiveTable display nowrap w-100">
                                <thead>
                                    <tr>
                                        <th>Booking ID</th>
                                        <th>Customer Name</th>
                                        <th>Service Name</th>
                                        <th>Service Name</th>
                                        <th>Status</th>
                                        <th>Date & Time</th>
                                        <th>Amount</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php for($b = 0; $b < 20; $b++): ?>
                                        <tr>
                                            <td data-label="Booking ID">BK-9123</td>
                                            <td data-label="Customer Name">Carolyn Perkins</td>
                                            <td data-label="Service Name">Hair Coloring</td>
                                            <td data-label="Service Name">Individual</td>
                                            <td data-label="Status" class="status paid-status">Booked</td>
                                            <td data-label="Date & Time">Apr 10, 2025 - 2:00 PM</td>
                                            <td data-label="Amount">$17.84</td>
                                            <td data-label="Action">
                                                <div class="dropdown">
                                                    <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                        <li>
                                                            <button class="dropdown-item complete fs-14 regular " type="button">
                                                                <i class="bi bi-check-circle complete-icon"></i>
                                                                Mark as Complete
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button class="dropdown-item cancel fs-14 regular" type="button">
                                                                <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>

                                            <td data-label="Booking ID">BK-9123</td>
                                            <td data-label="Customer Name">Carolyn Perkins</td>
                                            <td data-label="Service Name">Hair Coloring</td>
                                            <td data-label="Service Name">Individual</td>
                                            <td data-label="Status" class="status unpaid-status">Cancelled</td>
                                            <td data-label="Date & Time">Apr 10, 2025 - 2:00 PM</td>
                                            <td data-label="Amount">$17.84</td>
                                            <td data-label="Action">
                                                <div class="dropdown">
                                                    <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                        <li>
                                                            <button class="dropdown-item complete fs-14 regular " type="button">
                                                                <i class="bi bi-check-circle complete-icon"></i>
                                                                Mark as Complete
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button class="dropdown-item cancel fs-14 regular" type="button">
                                                                <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td data-label="Booking ID">BK-9123</td>
                                            <td data-label="Customer Name">Carolyn Perkins</td>
                                            <td data-label="Service Name">Hair Coloring</td>
                                            <td data-label="Service Name">Individual</td>
                                            <td data-label="Status" class="status pending-status">Pending</td>
                                            <td data-label="Date & Time">Apr 10, 2025 - 2:00 PM</td>
                                            <td data-label="Amount">$17.84</td>
                                            <td data-label="Action">
                                                <div class="dropdown">
                                                    <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                        <li>
                                                            <button class="dropdown-item complete fs-14 regular " type="button">
                                                                <i class="bi bi-check-circle complete-icon"></i>
                                                                Mark as Complete
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button class="dropdown-item cancel fs-14 regular" type="button">
                                                                <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td data-label="Booking ID">BK-9123</td>
                                            <td data-label="Customer Name">Carolyn Perkins</td>
                                            <td data-label="Service Name">Hair Coloring</td>
                                            <td data-label="Service Name">Individual</td>
                                            <td data-label="Status" class="status reschedule-status">Reschedule</td>
                                            <td data-label="Date & Time">Apr 10, 2025 - 2:00 PM</td>
                                            <td data-label="Amount">$17.84</td>
                                            <td data-label="Action">
                                                <div class="dropdown">
                                                    <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                        <li>
                                                            <button class="dropdown-item complete fs-14 regular " type="button">
                                                                <i class="bi bi-check-circle complete-icon"></i>
                                                                Mark as Complete
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button class="dropdown-item cancel fs-14 regular" type="button">
                                                                <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endfor; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php echo $__env->make('dashboard.templates.modal.add-booking-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git-file\anders\resources\views/dashboard/business/business-booking.blade.php ENDPATH**/ ?>